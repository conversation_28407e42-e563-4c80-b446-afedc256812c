import ZnOrder from '#models/zn_order';
import { ShopifyService } from '#services/shopify/shopify_service';
import { args, BaseCommand } from '@adonisjs/core/ace';
import type { CommandOptions } from '@adonisjs/core/types/ace';

export default class SyncFulfilSuppliers extends BaseCommand {
  static commandName = 'check:order'
  static description = 'Check if order exists on Shopify'

  static options: CommandOptions = {
    startApp: true,
  }

  @args.string({
    required: true,
    description: "Id of Order"
  })
  declare orderId: string

  async run() {
    const shopifyService = new ShopifyService()

    try {
      const order = await ZnOrder.find(this.orderId)
      if (!order) { return this.logger.error("Databse Order Not Found") }

      console.log('order.shopifyId', order.shopifyId);

      const shopifyRes = await shopifyService.fetchOrdersWithIds([order.shopifyId])
      const shopifyOrder = shopifyRes.orders[0]

      console.log('shopifyRes.orders', shopifyRes.orders);

      if (!shopifyOrder) { return this.logger.error("Shopify Order Not Found") }

      console.log('shopifyOrder', shopifyOrder);


    } catch (error) {
      console.log(error.response.data);
    }
  }
}
