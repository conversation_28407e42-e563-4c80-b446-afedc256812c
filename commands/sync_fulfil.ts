import { BaseCommand } from '@adonisjs/core/ace';
import type { CommandOptions } from '@adonisjs/core/types/ace';
import { FulfilService } from "../app/services/fulfil_service.js";

export default class SyncFulfilSuppliers extends BaseCommand {
  static commandName = 'sync:fulfil'
  static description = 'Sync data from fulfil data warehouse'

  static options: CommandOptions = {
    startApp: true,
  }
  async run() {
    const fulfilService = new FulfilService()
    try {
      const response = await fulfilService.pushProductWithBrand({
        name: 'EcoBottle 500ml',
        description: 'Reusable stainless steel bottle',
        code: 'TEST005-500',
        list_price: '24.9',
        cost_price: '9.5',
        vendor: { name: 'GreenFlow01' }
      })
      console.log(response)
    } catch (error) {
      console.log(error.response.data);
    }
  }
}
