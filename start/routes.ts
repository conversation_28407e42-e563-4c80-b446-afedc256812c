/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'

import v1Routes from './route/v1/index.js'
import v2Routes from './route/v2/index.js'
import swaggerRoutes from './route/swagger/swagger.js'
import swaggerAdminRoutes from './route/swagger/swagger_admin.js'
import { execSync } from 'node:child_process'
import { HttpContext } from '@adonisjs/core/http'

router.get('/', async ({ request, response }: HttpContext) => {
  const { info } = request.all();
  if (info && info === 'commit') {
    const commitHash = execSync('git rev-parse HEAD').toString().trim()

    return response.json({
      commit: commitHash,
      timestamp: new Date().toISOString()
    })
  } else {
    return 'it works'
    // return view.render('mails/shop/login_code.edge')}
  }
})

v1Routes()
v2Routes()

swaggerRoutes()
swaggerAdminRoutes()
