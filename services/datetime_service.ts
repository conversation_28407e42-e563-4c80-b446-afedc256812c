import { DateTime } from 'luxon'

/**
 * DateTime service for consistent timezone handling across the application
 * Default timezone is set to US Eastern Time (America/New_York)
 */
export class DateTimeService {
  private static readonly DEFAULT_TIMEZONE = 'America/New_York'

  /**
   * Get current DateTime in US timezone
   */
  static nowUS(): DateTime {
    return DateTime.now().setZone(this.DEFAULT_TIMEZONE)
  }

  /**
   * Convert JS Date to DateTime with US timezone
   */
  static fromJSDateUS(date: Date): DateTime {
    return DateTime.fromJSDate(date).setZone(this.DEFAULT_TIMEZONE)
  }

  /**
   * Convert ISO string to DateTime with US timezone
   */
  static fromISOUS(iso: string): DateTime {
    return DateTime.fromISO(iso).setZone(this.DEFAULT_TIMEZONE)
  }

  /**
   * Convert DateTime to US timezone
   */
  static toUS(dateTime: DateTime): DateTime {
    return dateTime.setZone(this.DEFAULT_TIMEZONE)
  }

  /**
   * Get current DateTime in local timezone (for backward compatibility)
   */
  static nowLocal(): DateTime {
    return DateTime.now()
  }

  /**
   * Convert JS Date to DateTime without timezone conversion (for backward compatibility)
   */
  static fromJSDateLocal(date: Date): DateTime {
    return DateTime.fromJSDate(date)
  }
}
