import { test } from '@japa/runner'
import { DateTime } from 'luxon'
import ZnSalonConstructionServiceSignup from '#models/zn_salon_construction_service_signup'

test.group('Salon Construction Service Timezone', () => {
  test('should save dates in US timezone', async ({ assert }) => {
    // Create a test date in Vietnam timezone
    const testDate = new Date('2025-07-23T10:00:00+07:00') // 10 AM Vietnam time
    
    const signupData = {
      fullName: 'Test User',
      businessName: 'Test Business',
      salonAddress: 'Test Address',
      phoneNumber: '+1234567890',
      emailAddress: '<EMAIL>',
      serviceInterest: ['full_renovation'],
      preferredStartDate: testDate,
      budgetRange: '$50-100K',
      consentConfirmed: true,
      signature: 'Test Signature',
      signatureDate: testDate,
    }

    // Make request to create salon construction service signup
    const response = await fetch('/salon-construction-service', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(signupData),
    })

    assert.equal(response.status, 201)
    
    const responseData = await response.json()
    const signupId = responseData.data.id

    // Fetch the created record from database
    const signup = await ZnSalonConstructionServiceSignup.find(signupId)
    assert.isNotNull(signup)

    // Check that dates are stored in US timezone
    if (signup?.preferredStartDate) {
      const storedDate = signup.preferredStartDate
      assert.equal(storedDate.zoneName, 'America/New_York')
    }

    if (signup?.signatureDate) {
      const storedDate = signup.signatureDate
      assert.equal(storedDate.zoneName, 'America/New_York')
    }

    // Clean up
    if (signup) {
      await signup.delete()
    }
  })

  test('should handle null dates correctly', async ({ assert }) => {
    const signupData = {
      fullName: 'Test User',
      businessName: 'Test Business',
      salonAddress: 'Test Address',
      phoneNumber: '+1234567890',
      emailAddress: '<EMAIL>',
      serviceInterest: ['interior_design'],
      budgetRange: '$50-100K',
      consentConfirmed: true,
      signature: 'Test Signature',
      // No preferredStartDate and signatureDate provided
    }

    const response = await fetch('/salon-construction-service', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(signupData),
    })

    assert.equal(response.status, 201)
    
    const responseData = await response.json()
    const signupId = responseData.data.id

    const signup = await ZnSalonConstructionServiceSignup.find(signupId)
    assert.isNotNull(signup)

    // preferredStartDate should be null
    assert.isNull(signup?.preferredStartDate)
    
    // signatureDate should be set to current US time
    if (signup?.signatureDate) {
      const storedDate = signup.signatureDate
      assert.equal(storedDate.zoneName, 'America/New_York')
      
      // Should be close to current time (within 1 minute)
      const now = DateTime.now().setZone('America/New_York')
      const diff = Math.abs(storedDate.diff(now).as('minutes'))
      assert.isBelow(diff, 1)
    }

    // Clean up
    if (signup) {
      await signup.delete()
    }
  })
})
