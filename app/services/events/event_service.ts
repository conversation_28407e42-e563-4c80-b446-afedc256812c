import { DateTime } from 'luxon'
import ZnEvent, { EEventStatus } from '#models/zn_event'
import ZnEventRegistration, { ERegistrationSource } from '#models/zn_event_registration'
import { Exception } from '@adonisjs/core/exceptions'
import type { ModelAttributes } from '@adonisjs/lucid/types/model'
import mail from '@adonisjs/mail/services/main'
import RegistrationSuccessfulNotification from '#mails/event/registration_successful_notification'
import env from '#start/env'
import string from '@adonisjs/core/helpers/string'
import ZnLanguage from '#models/zn_language'
import ZnEventTime from '#models/zn_event_times'
import ZnProductVariant from '#models/zn_product_variant'
import { DateTimeService } from '../../datetime_service.js'

export interface IEventRegistrationData {
  name: string
  email: string
  phone: string
  languages: string[]
  occupation: string
  userId?: string
  source?: ERegistrationSource
}

export default class EventService {
  /**
   * Generate QR code for an event
   */
  public async generateEventQR(eventId: string): Promise<string> {
    try {
      const qrCodeDataUrl = `data:image/png;base64,placeholder-for-qr-code-${eventId}`

      const event = await ZnEvent.findOrFail(eventId)
      event.qrCode = qrCodeDataUrl
      await event.save()

      return qrCodeDataUrl
    } catch (error) {
      console.error('Error generating event QR code:', error)
      throw new Exception('Failed to generate QR code for event', { status: 500 })
    }
  }

  /**
   * Generate QR code for registration
   */
  public async generateRegistrationQR(
    registrationId: string,
    email: string,
    phone: string
  ): Promise<string> {
    try {
      const registration = await ZnEventRegistration.findOrFail(registrationId)
      const event = await ZnEvent.findOrFail(registration.eventId)

      const qrData = btoa(`${event.id},${email},${phone}`)
      const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${qrData}`

      registration.qrCode = qrCodeUrl
      await registration.save()

      return qrCodeUrl
    } catch (error) {
      console.error('Error generating registration QR code:', error)
      throw new Exception('Failed to generate QR code for registration', { status: 500 })
    }
  }

  /**
   * Register for an event
   */
  public async registerEvent(
    eventId: string,
    data: IEventRegistrationData,
    performCheckIn: boolean = false,
    checkedInByAdminId?: string,
    checkInTime?: DateTime
  ): Promise<ZnEventRegistration> {
    try {
      const event = await ZnEvent.findOrFail(eventId)

      if (event.status === EEventStatus.CANCELLED) {
        throw new Exception('This event has been cancelled', { status: 400 })
      }

      if (event.status === EEventStatus.COMPLETED) {
        throw new Exception('This event has already ended', { status: 400 })
      }

      const existingRegistration = await ZnEventRegistration.query()
        .where('eventId', eventId)
        .where((query) => {
          if (data.email) query.where('email', data.email)
          else if (data.phone) query.where('phone', data.phone)
        })
        .first()

      if (existingRegistration) {
        if (performCheckIn && !existingRegistration.checkedIn) {
          existingRegistration.checkedIn = true
          existingRegistration.checkedInAt = checkInTime || DateTime.now()
          existingRegistration.checkedInByAdminId = checkedInByAdminId || null
          await existingRegistration.save()
        }
        return existingRegistration
      }

      const registration = new ZnEventRegistration()
      registration.eventId = eventId
      registration.name = data.name
      registration.email = data.email
      registration.phone = data.phone
      registration.occupation = data.occupation
      registration.userId = data.userId || null
      registration.source = data.source || ERegistrationSource.WEB

      if (performCheckIn) {
        registration.checkedIn = true
        registration.checkedInAt = checkInTime || DateTimeService.nowUS()
        registration.checkedInByAdminId = checkedInByAdminId || null
      } else {
        registration.checkedIn = false
      }

      await registration.save()

      const languages = await ZnLanguage.query().whereIn('code', data.languages)
      await registration.related('languages').attach(languages.map((lang) => lang.id))
      await registration.load('languages')

      registration.qrCode = await this.generateRegistrationQR(
        registration.id,
        registration.email,
        registration.phone
      )

      return registration
    } catch (error) {
      if (error instanceof Exception) {
        throw error
      }
      console.error('Error registering for event:', error)
      throw new Exception('Failed to register for event', { status: 500 })
    }
  }

  public async resendEmail(registrationId: string) {
    const registration = await ZnEventRegistration.findOrFail(registrationId)

    if (registration.lastSendingEmailTime) {
      const now = DateTime.local()
      const diff = now.diff(registration.lastSendingEmailTime).as('seconds')

      if (diff < 60) {
        return {
          success: false,
          lastSendingEmailTime: registration.lastSendingEmailTime,
          canResendIn: 60 - diff,
        }
      }
    }

    const updatedRegistration = await this.sendRegistrationEmail(registration.id)

    return {
      success: true,
      lastSendingEmailTime: updatedRegistration?.lastSendingEmailTime,
    }
  }

  /**
   * Check in a participant
   */
  public async checkInParticipant(
    registrationId: string,
    adminId?: string,
    checkInTime?: DateTime
  ): Promise<ZnEventRegistration> {
    try {
      const registration = await ZnEventRegistration.findOrFail(registrationId)

      if (registration.checkedIn) {
        return registration
      }

      registration.checkedIn = true
      registration.checkedInAt = checkInTime || DateTime.now()
      registration.checkedInByAdminId = adminId || null
      await registration.save()

      return registration
    } catch (error) {
      console.error('Error checking in participant:', error)
      throw new Exception('Failed to check in participant', { status: 500 })
    }
  }

  /**
   * Find registration by email or phone
   */
  public async findRegistrationByContact(
    eventId: string,
    contactInfo: string
  ): Promise<ZnEventRegistration | null> {
    try {
      // @ts-ignore
      return await ZnEventRegistration.query({ mode: 'write' })
        .where('eventId', eventId)
        .where((query) => {
          query.where('email', contactInfo).orWhere('phone', contactInfo)
        })
        .whereNull('deletedAt')
        .first()
    } catch (error) {
      console.error('Error finding registration:', error)
      throw new Exception('Failed to find registration', { status: 500 })
    }
  }

  /**
   * Check and update existing registration with userId if matched
   */
  public async checkAndUpdateRegistrationUserId(
    eventId: string,
    data: IEventRegistrationData
  ): Promise<ZnEventRegistration | null> {
    try {
      if (!data.userId) {
        return null
      }

      let existingRegistration: ZnEventRegistration | null = null

      if (data.email) {
        existingRegistration = await ZnEventRegistration.query()
          .where('eventId', eventId)
          .where('email', data.email)
          .whereNull('deletedAt')
          .first()
      }

      if (!existingRegistration && data.phone) {
        existingRegistration = await ZnEventRegistration.query()
          .where('eventId', eventId)
          .where('phone', data.phone)
          .whereNull('deletedAt')
          .first()
      }

      if (existingRegistration && !existingRegistration.userId) {
        existingRegistration.userId = data.userId
        await existingRegistration.save()
        return existingRegistration
      }

      return existingRegistration
    } catch (error) {
      console.error('Error checking/updating registration userId:', error)
      throw new Exception('Failed to update registration user data', { status: 500 })
    }
  }

  /**
   * Create a new event
   */
  public async createEvent(
    data: Partial<ModelAttributes<ZnEvent>>,
    timeRanges: any[],
    thumbnails: any[]
  ): Promise<ZnEvent> {
    try {
      let event: ZnEvent

      if (data.id) {
        event = await ZnEvent.findOrFail(data.id)
      } else {
        event = new ZnEvent()
      }

      event.title = data.title!
      event.description = data.description || null
      event.location = data.location!
      event.capacity = data.capacity || 0
      event.status = data.status || EEventStatus.SCHEDULED
      event.createdByAdminId = data.createdByAdminId || event.createdByAdminId || null

      event.slug = data.slug || null

      await event.save()

      for (const timeRange of timeRanges) {
        const startTime = timeRange.startTime
          ? DateTimeService.fromJSDateUS(new Date(timeRange.startTime))
          : undefined
        const endTime = timeRange.endTime
          ? DateTimeService.fromJSDateUS(new Date(timeRange.endTime))
          : undefined

        await ZnEventTime.create({
          startTime,
          endTime,
          eventId: event.id,
        })
      }

      if (thumbnails) {
        const syncData = thumbnails.reduce(
          (accData, thumbnail) => ({
            ...accData,
            [thumbnail.id]: {
              renderPosition:
                thumbnail.renderPosition && thumbnail.renderPosition.length > 0
                  ? thumbnail.renderPosition
                  : 'register-page',
            },
          }),
          {}
        )
        await event.related('thumbnails').sync(syncData)
      }

      if (!data.id) {
        await this.generateEventQR(event.id)
      }

      return event
    } catch (error) {
      console.error('Error creating/updating event:', error)
      throw new Exception('Failed to create/update event', { status: 500 })
    }
  }

  /**
   * Send success registration email
   */
  public async sendRegistrationEmail(registrationId: string) {
    // @ts-ignore
    const registration = await ZnEventRegistration.query({ mode: 'write' })
      .where('id', registrationId)
      .preload('event', (query) => {
        query.preload('timeRanges', (query) => {
          query.orderBy('startTime', 'asc')
        })
      })
      .first()

    if (!registration || !registration.event) {
      return
    }

    await mail
      .send(new RegistrationSuccessfulNotification(registration, registration.event))
      .then(() => {
        // Registration email send successfully
        console.log(`Registration sent for email: ${registration.email}`)
      })
      .catch((error) => {
        console.log('Error when sending registration mail', error)
      })

    registration.lastSendingEmailTime = DateTime.now()
    return await registration.save()
  }

  public async generateSuggestedSlugAndUrl(
    title: string
  ): Promise<{ slug: string; shareUrl: string }> {
    try {
      const baseSlug = string.slug(title)
      let slug = baseSlug
      let counter = 1

      while (true) {
        const existingEvent = await ZnEvent.query().where('slug', slug).first()

        if (!existingEvent) {
          break
        }

        slug = `${baseSlug}-${counter}`
        counter++
      }

      const domain = env.get('ADS_WEBSITE_DOMAIN')
      const shareUrl = `${domain}/event/${slug}/register`

      return {
        slug,
        shareUrl,
      }
    } catch (error) {
      console.error('Error generating suggested slug and URL:', error)
      throw new Exception('Failed to generate suggested slug and URL', { status: 500 })
    }
  }

  async getPrizes() {
    const grandPrizeVariants = env.get('EVENT_RAFFLE_GRAND_PRIZE_VARIANTS')?.split(',')
    const specialPrizeVariants = env.get('EVENT_RAFFLE_SPECIAL_PRIZE_VARIANTS')?.split(',')
    if (!grandPrizeVariants || !specialPrizeVariants) {
      return []
    }

    const grandVariants = await ZnProductVariant.query()
      .whereIn('sku', grandPrizeVariants)
      .preload('image')

    const specialVariants = await ZnProductVariant.query()
      .whereIn('sku', specialPrizeVariants)
      .preload('image')

    return {
      grandPrize: grandVariants,
      specialPrize: specialVariants,
    }
  }
}
